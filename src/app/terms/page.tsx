"use client";

import { useTranslations } from "next-intl";
import Navigation from "@/components/Navigation";
import FooterSection from "@/components/sections/FooterSection";

export default function TermsPage() {
  const t = useTranslations("Terms");

  return (
    <div className="min-h-screen">
      <Navigation />

      {/* Terms of Service Content */}
      <section className="legal-page">
        <div className="container">
          <div className="legal-content">
            <h1>{t("title")}</h1>
            <p className="last-updated">{t("lastUpdated")}</p>

            <p>{t("introduction")}</p>

            <h2>{t("sections.acceptance.title")}</h2>
            <p>{t("sections.acceptance.content")}</p>

            <h2>{t("sections.service.title")}</h2>
            <p>{t("sections.service.description")}</p>
            <ul>
              <li>{t("sections.service.features.personas")}</li>
              <li>{t("sections.service.features.content")}</li>
              <li>{t("sections.service.features.posting")}</li>
              <li>{t("sections.service.features.trends")}</li>
            </ul>

            <h2>{t("sections.accounts.title")}</h2>
            <h3>{t("sections.accounts.creation.title")}</h3>
            <p>{t("sections.accounts.creation.description")}</p>
            <ul>
              <li>{t("sections.accounts.creation.requirements.accurate")}</li>
              <li>{t("sections.accounts.creation.requirements.security")}</li>
              <li>{t("sections.accounts.creation.requirements.notify")}</li>
              <li>
                {t("sections.accounts.creation.requirements.responsible")}
              </li>
            </ul>

            <h3>{t("sections.accounts.age.title")}</h3>
            <p>{t("sections.accounts.age.content")}</p>

            <h2>{t("sections.content.title")}</h2>
            <h3>{t("sections.content.ownership.title")}</h3>
            <p>{t("sections.content.ownership.description")}</p>
            <ul>
              <li>{t("sections.content.ownership.types.photos")}</li>
              <li>{t("sections.content.ownership.types.knowledge")}</li>
              <li>{t("sections.content.ownership.types.personas")}</li>
            </ul>

            <h3>{t("sections.content.license.title")}</h3>
            <p>{t("sections.content.license.content")}</p>

            <h3>{t("sections.content.guidelines.title")}</h3>
            <p>{t("sections.content.guidelines.description")}</p>
            <ul>
              <li>{t("sections.content.guidelines.prohibited.illegal")}</li>
              <li>{t("sections.content.guidelines.prohibited.copyright")}</li>
              <li>{t("sections.content.guidelines.prohibited.hate")}</li>
              <li>{t("sections.content.guidelines.prohibited.explicit")}</li>
              <li>{t("sections.content.guidelines.prohibited.activities")}</li>
            </ul>

            <h2>{t("sections.aiContent.title")}</h2>
            <h3>{t("sections.aiContent.ownership.title")}</h3>
            <p>{t("sections.aiContent.ownership.content")}</p>

            <h3>{t("sections.aiContent.responsibility.title")}</h3>
            <p>{t("sections.aiContent.responsibility.content")}</p>

            <h2>{t("sections.prohibited.title")}</h2>
            <p>{t("sections.prohibited.description")}</p>
            <ul>
              <li>{t("sections.prohibited.items.illegal")}</li>
              <li>{t("sections.prohibited.items.access")}</li>
              <li>{t("sections.prohibited.items.interfere")}</li>
              <li>{t("sections.prohibited.items.fake")}</li>
              <li>{t("sections.prohibited.items.violate")}</li>
            </ul>

            <h2>{t("sections.payment.title")}</h2>
            <h3>{t("sections.payment.trial.title")}</h3>
            <p>{t("sections.payment.trial.content")}</p>

            <h3>{t("sections.payment.subscription.title")}</h3>
            <p>{t("sections.payment.subscription.content")}</p>

            <h3>{t("sections.payment.cancellation.title")}</h3>
            <p>{t("sections.payment.cancellation.content")}</p>

            <h2>{t("sections.intellectual.title")}</h2>
            <p>{t("sections.intellectual.content")}</p>

            <h2>{t("sections.warranties.title")}</h2>
            <p>{t("sections.warranties.content")}</p>

            <h2>{t("sections.liability.title")}</h2>
            <p>{t("sections.liability.content")}</p>

            <h2>{t("sections.indemnification.title")}</h2>
            <p>{t("sections.indemnification.content")}</p>

            <h2>{t("sections.changes.title")}</h2>
            <p>{t("sections.changes.content")}</p>

            <h2>{t("sections.termination.title")}</h2>
            <p>{t("sections.termination.content")}</p>

            <h2>{t("sections.governing.title")}</h2>
            <p>{t("sections.governing.content")}</p>

            <h2>{t("sections.contact.title")}</h2>
            <p>
              {t("sections.contact.content")}{" "}
              <a href="mailto:<EMAIL>">
                <EMAIL>
              </a>
              .
            </p>
          </div>
        </div>
      </section>

      <FooterSection />

      <style jsx>{`
        .legal-page {
          padding: 120px 0 80px;
          min-height: 100vh;
        }
        .legal-content {
          max-width: 800px;
          margin: 0 auto;
          background: var(--bg-secondary);
          border: 1px solid var(--border-color);
          border-radius: 16px;
          padding: 48px;
        }
        .legal-content h1 {
          font-size: 36px;
          margin-bottom: 16px;
          background: var(--gradient-primary);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .legal-content .last-updated {
          color: var(--text-secondary);
          font-size: 14px;
          margin-bottom: 40px;
        }
        .legal-content h2 {
          font-size: 24px;
          margin-top: 32px;
          margin-bottom: 16px;
          color: var(--text-primary);
        }
        .legal-content h3 {
          font-size: 18px;
          margin-top: 24px;
          margin-bottom: 12px;
          color: var(--text-primary);
        }
        .legal-content p,
        .legal-content li {
          line-height: 1.8;
          color: var(--text-secondary);
          margin-bottom: 16px;
        }
        .legal-content ul {
          margin-left: 24px;
          margin-bottom: 16px;
        }
        .legal-content a {
          color: var(--accent-red);
          text-decoration: none;
          transition: color 0.2s ease;
        }
        .legal-content a:hover {
          color: var(--accent-purple);
        }
        @media (max-width: 768px) {
          .legal-content {
            padding: 32px 24px;
          }
          .legal-content h1 {
            font-size: 28px;
          }
        }
      `}</style>
    </div>
  );
}
